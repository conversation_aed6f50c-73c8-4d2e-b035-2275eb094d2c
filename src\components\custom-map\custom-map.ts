

interface MarkerData {
    x: number;
    y: number;
    label: string;
}

const originalWidth = 6000;
const originalHeight = 3000;

// Exemple de données à afficher
const markerList: MarkerData[] = [
    { x: 5000, y: 835, label: "Point A" },
    { x: 3000, y: 1400, label: "Point B" },
    { x: 4500, y: 1600, label: "Point C" },
];

export default function initCustomMap() {
    const container = document.querySelector(".image-container") as HTMLElement;
    const infoBox = document.getElementById("marker-info") as HTMLElement;

    markerList.forEach((markerData, index) => {
        const marker = document.createElement("div");
        marker.className = "marker";
        marker.setAttribute("data-index", index.toString());

        const xPercent = (markerData.x / originalWidth) * 100;
        const yPercent = (markerData.y / originalHeight) * 100;

        marker.style.left = `${xPercent}%`;
        marker.style.top = `${yPercent}%`;

        marker.addEventListener("click", () => {
            document.querySelectorAll(".marker").forEach(m => m.classList.remove("active"));
            marker.classList.add("active");

            infoBox.innerText = markerData.label;
            infoBox.style.left = `${xPercent}%`;
            infoBox.style.top = `${yPercent}%`;
            infoBox.style.display = "block";
        });

        container.appendChild(marker);
    });
}
